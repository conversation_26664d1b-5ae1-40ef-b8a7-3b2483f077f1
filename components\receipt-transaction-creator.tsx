"use client";

import React, { useState } from "react";
import { ReceiptUpload } from "./receipt-upload";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrency, convertAmountFormMiliunits } from "@/lib/utils";
import { useGetAccounts } from "@/features/accounts/api/use-get-accounts";
import { useGetCategoriesAll } from "@/features/categories/api/use-get-categories-all";
import { useGetTags } from "@/features/tags/api/use-get-tags";
import { TagChip } from "@/features/tags/components/tag-chip";
import { TagSelector } from "@/features/tags/components/tag-selector";
import { toast } from "sonner";
import { 
  Receipt, 
  DollarSign, 
  Calendar, 
  Store, 
  FileText, 
  CheckCircle2,
  Edit3,
  Save,
  X,
  Search,
  Plus
} from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Switch } from "@/components/ui/switch";

interface Tag {
  id: string;
  name: string;
  color?: string | null;
}

interface ReceiptTransaction {
  amount: number;
  payee: string;
  notes?: string;
  date: string;
  accountId?: string;
  categoryId?: string;
  projectId?: string;
  isIncome?: boolean;
  detailsTransactions: Array<{
    name?: string;
    quantity?: number;
    unitPrice?: number;
    amount: number;
    categoryId?: string;
    projectId?: string;
    isIncome?: boolean;
    suggestedTagIds?: string[];
    suggestedNewTags?: string[];
    selectedTags?: Tag[];
  }>;
}

interface NewCategory {
  id: string;
  name: string;
  isNew: boolean;
}

interface ProcessedReceiptData {
  transactions: ReceiptTransaction[];
  suggestedCategories: string[];
  confidence: number;
  processingNotes?: string;
  userAccounts: Array<{ id: string; name: string }>;
  userCategories: Array<{ id: string; name: string }>;
  tagProcessingResult?: {
    itemTagSuggestions: Array<{
      itemName: string;
      existingTagIds: string[];
      suggestedNewTags: string[];
    }>;
    confidence: number;
    processingNotes?: string;
  };
}

export function ReceiptTransactionCreator() {
  const [processedData, setProcessedData] = useState<ProcessedReceiptData | null>(null);
  const [editingTransactions, setEditingTransactions] = useState<ReceiptTransaction[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [newCategories, setNewCategories] = useState<NewCategory[]>([]);
  const [newTags, setNewTags] = useState<Array<{ name: string; color?: string }>>([]);
  
  const { data: accounts } = useGetAccounts();
  const { data: categories } = useGetCategoriesAll();
  const { data: tags } = useGetTags();

  const handleReceiptsProcessed = (data: { data: ProcessedReceiptData }) => {
    const additionalSuggestedCategories: string[] = [];
    
    // Clean up the data and detect invalid category IDs
    const cleanedData = {
      ...data.data,
      transactions: data.data.transactions.map(transaction => {
        // Check if categoryId is actually a category name (not a valid ID)
        const isValidCategoryId = transaction.categoryId && categories?.some(cat => cat.id === transaction.categoryId);
        let cleanedCategoryId = transaction.categoryId;
        
        if (transaction.categoryId === "undefined") {
          cleanedCategoryId = undefined;
        } else if (transaction.categoryId && !isValidCategoryId) {
          // CategoryId is probably a category name, add to suggestions
          additionalSuggestedCategories.push(transaction.categoryId);
          // Set the categoryId to a temporary new category ID that we'll create
          cleanedCategoryId = `new-suggested-${additionalSuggestedCategories.length - 1}`;
        }

        return {
          ...transaction,
          accountId: transaction.accountId === "undefined" ? undefined : transaction.accountId,
          categoryId: cleanedCategoryId,
          projectId: transaction.projectId === "undefined" ? undefined : transaction.projectId,
          notes: transaction.notes === "undefined" ? undefined : transaction.notes,
          isIncome: transaction.isIncome ?? false,
          detailsTransactions: transaction.detailsTransactions.map(detail => {
            // Check if detail categoryId is actually a category name
            const isValidDetailCategoryId = detail.categoryId && categories?.some(cat => cat.id === detail.categoryId);
            let cleanedDetailCategoryId = detail.categoryId;
            
            if (detail.categoryId === "undefined") {
              cleanedDetailCategoryId = undefined;
            } else if (detail.categoryId && !isValidDetailCategoryId) {
              // CategoryId is probably a category name, add to suggestions
              if (!additionalSuggestedCategories.includes(detail.categoryId)) {
                additionalSuggestedCategories.push(detail.categoryId);
              }
              const categoryIndex = additionalSuggestedCategories.findIndex(cat => cat === detail.categoryId);
              cleanedDetailCategoryId = `new-suggested-${categoryIndex}`;
            }

            return {
              ...detail,
              name: detail.name === "undefined" ? undefined : detail.name,
              categoryId: cleanedDetailCategoryId,
              projectId: detail.projectId === "undefined" ? undefined : detail.projectId,
              quantity: detail.quantity === "undefined" ? undefined : detail.quantity,
              unitPrice: detail.unitPrice === "undefined" ? undefined : detail.unitPrice,
              isIncome: detail.isIncome ?? false,
            };
          })
        };
      })
    };
    
    // Combine original suggested categories with detected invalid category IDs
    const allSuggestedCategories = [
      ...cleanedData.suggestedCategories,
      ...additionalSuggestedCategories.filter(cat => !cleanedData.suggestedCategories.includes(cat))
    ];
    
    // Process tag suggestions and initialize selected tags
    const processedTransactions = cleanedData.transactions.map(transaction => ({
      ...transaction,
      detailsTransactions: transaction.detailsTransactions.map(detail => {
        const selectedTags: Tag[] = [];
        
        // Add existing tags based on suggestions
        if (detail.suggestedTagIds && tags) {
          const existingTags = tags.filter(tag => detail.suggestedTagIds!.includes(tag.id));
          selectedTags.push(...existingTags);
        }
        
        return {
          ...detail,
          selectedTags
        };
      })
    }));
    
    // Collect all suggested new tags
    const allSuggestedNewTags = new Set<string>();
    cleanedData.transactions.forEach(transaction => {
      transaction.detailsTransactions.forEach(detail => {
        if (detail.suggestedNewTags) {
          detail.suggestedNewTags.forEach(tagName => allSuggestedNewTags.add(tagName));
        }
      });
    });
    
    setProcessedData({
      ...cleanedData,
      suggestedCategories: allSuggestedCategories,
      transactions: processedTransactions
    });
    setEditingTransactions([...processedTransactions]);
    
    // Create new category options from all suggested categories
    const suggestedNewCategories: NewCategory[] = allSuggestedCategories.map((categoryName, index) => ({
      id: `new-suggested-${index}`,
      name: categoryName,
      isNew: true
    }));
    setNewCategories(suggestedNewCategories);
    
    // Create new tag options from all suggested tags
    const suggestedNewTags = Array.from(allSuggestedNewTags).map(tagName => ({
      name: tagName
    }));
    setNewTags(suggestedNewTags);
  };

  const updateTransaction = (index: number, updates: Partial<ReceiptTransaction>) => {
    setEditingTransactions(prev => 
      prev.map((transaction, i) => 
        i === index ? { ...transaction, ...updates } : transaction
      )
    );
  };

  const updateDetailTransaction = (
    transactionIndex: number, 
    detailIndex: number, 
    updates: Partial<ReceiptTransaction['detailsTransactions'][0]>
  ) => {
    setEditingTransactions(prev => 
      prev.map((transaction, i) => 
        i === transactionIndex
          ? {
              ...transaction,
              detailsTransactions: transaction.detailsTransactions.map((detail, di) =>
                di === detailIndex ? { ...detail, ...updates } : detail
              )
            }
          : transaction
      )
    );
  };

  const updateDetailTags = (
    transactionIndex: number,
    detailIndex: number,
    newTags: Tag[]
  ) => {
    updateDetailTransaction(transactionIndex, detailIndex, { selectedTags: newTags });
  };

  const addNewCategory = (categoryName: string) => {
    const newCategory: NewCategory = {
      id: `new-${Date.now()}`,
      name: categoryName,
      isNew: true
    };
    setNewCategories(prev => [...prev, newCategory]);
    return newCategory.id;
  };

  const getAllCategories = () => {
    const existingCategories = categories?.map(cat => ({ ...cat, isNew: false })) || [];
    return [...existingCategories, ...newCategories];
  };

  // CategoryPicker component
  const CategoryPicker = ({ 
    value, 
    onValueChange, 
    placeholder = "Select category",
    className = ""
  }: {
    value?: string;
    onValueChange: (value?: string) => void;
    placeholder?: string;
    className?: string;
  }) => {
    const [open, setOpen] = useState(false);
    const allCategories = getAllCategories();
    
    const selectedCategory = allCategories.find(cat => cat.id === value);
    
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={`w-full justify-between ${className}`}
          >
            {selectedCategory ? (
              <span className="flex items-center gap-2">
                {selectedCategory.name}
                {selectedCategory.isNew && <Badge variant="secondary" className="text-xs">New</Badge>}
              </span>
            ) : (
              placeholder
            )}
            <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Search categories..." />
            <CommandList>
              <CommandEmpty>No category found.</CommandEmpty>
              <CommandGroup>
                <CommandItem
                  value="none"
                  onSelect={() => {
                    onValueChange(undefined);
                    setOpen(false);
                  }}
                >
                  <span className="text-muted-foreground">None</span>
                </CommandItem>
                {categories?.map((category) => (
                  <CommandItem
                    key={category.id}
                    value={category.name}
                    onSelect={() => {
                      onValueChange(category.id);
                      setOpen(false);
                    }}
                  >
                    {category.name}
                  </CommandItem>
                ))}
                {newCategories.map((category) => (
                  <CommandItem
                    key={category.id}
                    value={category.name}
                    onSelect={() => {
                      onValueChange(category.id);
                      setOpen(false);
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <span>{category.name}</span>
                      <Badge variant="secondary" className="text-xs">New</Badge>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
              <Separator />
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    // Focus on the custom category input
                    const input = document.querySelector('input[placeholder="Enter new category name..."]') as HTMLInputElement;
                    input?.focus();
                  }}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Create new category...
                </CommandItem>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  };

  const createTransactions = async () => {
    if (!editingTransactions.length) return;

    // Validate that all transactions have required fields
    const invalidTransactions = editingTransactions.filter(t => !t.accountId);
    if (invalidTransactions.length > 0) {
      toast.error("Please select an account for all transactions");
      return;
    }

    setIsCreating(true);

    try {
      // Collect all unique new tags from selected tags and newTags list
      const allNewTags = new Set<string>();
      
      // Add from newTags list
      newTags.forEach(tag => {
        allNewTags.add(tag.name.toLowerCase().trim());
      });
      
      // Add from selected tags that start with "new-"
      editingTransactions.forEach(transaction => {
        transaction.detailsTransactions.forEach(detail => {
          if (detail.selectedTags) {
            detail.selectedTags.forEach(tag => {
              if (tag.id.startsWith('new-')) {
                allNewTags.add(tag.name.toLowerCase().trim());
              }
            });
          }
        });
      });
      
      // Convert to newTags format
      const finalNewTags = Array.from(allNewTags).map(tagName => ({
        name: tagName,
        color: newTags.find(t => t.name.toLowerCase().trim() === tagName)?.color
      }));

      // Debug logging
      console.log('Sending transaction data:');
      console.log('Transactions with tags:', editingTransactions.map(t => ({
        payee: t.payee,
        detailsTransactions: t.detailsTransactions.map(d => ({
          name: d.name,
          selectedTags: d.selectedTags
        }))
      })));
      console.log('New tags to create:', finalNewTags);

      const response = await fetch('/api/receipts/create-transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactions: editingTransactions,
          newCategories: newCategories,
          newTags: finalNewTags,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      toast.success(result.message || "Transactions created successfully!");
      
      // Clear the form
      setProcessedData(null);
      setEditingTransactions([]);

    } catch (error) {
      console.error('Transaction creation error:', error);
      toast.error('Failed to create transactions');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setProcessedData(null);
    setEditingTransactions([]);
    setNewCategories([]);
    setNewTags([]);
  };

  if (processedData && editingTransactions.length > 0) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Review Extracted Transactions
              </span>
              <div className="flex items-center gap-2">
                <Badge variant={processedData.confidence > 0.8 ? "default" : "secondary"}>
                  {Math.round(processedData.confidence * 100)}% confidence
                </Badge>
                <Button onClick={resetForm} variant="outline" size="sm">
                  <X className="h-4 w-4 mr-2" />
                  Start Over
                </Button>
              </div>
            </CardTitle>
            <CardDescription>
              Review and edit the extracted transaction data before creating transactions.
              {processedData.processingNotes && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                  <strong>Note:</strong> {processedData.processingNotes}
                </div>
              )}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Transactions */}
        <div className="space-y-4">
          {editingTransactions.map((transaction, transactionIndex) => (
            <Card key={transactionIndex}>
              <CardHeader>
                <CardTitle className="text-lg">
                  Transaction {transactionIndex + 1}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Main transaction fields */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor={`merchant-${transactionIndex}`}>Merchant</Label>
                    <Input
                      id={`merchant-${transactionIndex}`}
                      value={transaction.payee}
                      onChange={(e) => updateTransaction(transactionIndex, { payee: e.target.value })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`amount-${transactionIndex}`}>Total Amount</Label>
                    <Input
                      id={`amount-${transactionIndex}`}
                      type="number"
                      step="0.01"
                      value={transaction.amount}
                      onChange={(e) => updateTransaction(transactionIndex, { amount: parseFloat(e.target.value) || 0 })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`date-${transactionIndex}`}>Date</Label>
                    <Input
                      id={`date-${transactionIndex}`}
                      type="date"
                      value={transaction.date}
                      onChange={(e) => updateTransaction(transactionIndex, { date: e.target.value })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`type-${transactionIndex}`}>Type</Label>
                    <div className="flex items-center space-x-2 pt-2">
                      <Switch
                        id={`type-${transactionIndex}`}
                        checked={transaction.isIncome || false}
                        onCheckedChange={(checked) => updateTransaction(transactionIndex, { isIncome: checked })}
                      />
                      <Label htmlFor={`type-${transactionIndex}`} className="text-sm">
                        {transaction.isIncome ? "Income" : "Expense"}
                      </Label>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`account-${transactionIndex}`}>Account *</Label>
                    <Select 
                      value={transaction.accountId || ""} 
                      onValueChange={(value) => updateTransaction(transactionIndex, { accountId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts?.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor={`category-${transactionIndex}`}>Category</Label>
                    <CategoryPicker
                      value={transaction.categoryId}
                      onValueChange={(value) => updateTransaction(transactionIndex, { categoryId: value })}
                      placeholder="Select category"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor={`notes-${transactionIndex}`}>Notes</Label>
                  <Textarea
                    id={`notes-${transactionIndex}`}
                    value={transaction.notes || ""}
                    onChange={(e) => updateTransaction(transactionIndex, { notes: e.target.value || undefined })}
                    placeholder="Additional notes..."
                  />
                </div>

                {/* Detail transactions */}
                {transaction.detailsTransactions.length > 0 && (
                  <div>
                    <Separator />
                    <h4 className="font-medium mt-4 mb-2">Receipt Items</h4>
                    <div className="overflow-x-auto">
                      {/* Header row */}
                      <div className="min-w-[900px] grid grid-cols-10 gap-2 items-center p-2 text-xs font-medium text-gray-600">
                        <div className="col-span-2">Item Name</div>
                        <div className="col-span-1">Qty</div>
                        <div className="col-span-1">Price</div>
                        <div className="col-span-1">Amount</div>
                        <div className="col-span-2">Category</div>
                        <div className="col-span-2">Tags</div>
                        <div className="col-span-1">Type</div>
                      </div>
                      <div className="space-y-2">
                        {transaction.detailsTransactions.map((detail, detailIndex) => (
                          <div key={detailIndex} className="min-w-[900px] grid grid-cols-10 gap-2 items-start p-2 bg-gray-50 rounded">
                          <div className="col-span-2">
                            <Input
                              placeholder="Item name"
                              value={detail.name || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { name: e.target.value || undefined })}
                            />
                          </div>
                          <div className="col-span-1">
                            <Input
                              type="number"
                              placeholder="Qty"
                              value={detail.quantity || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { quantity: parseFloat(e.target.value) || undefined })}
                            />
                          </div>
                          <div className="col-span-1">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Price"
                              value={detail.unitPrice || ""}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { unitPrice: parseFloat(e.target.value) || undefined })}
                            />
                          </div>
                          <div className="col-span-1">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Amount"
                              value={detail.amount}
                              onChange={(e) => updateDetailTransaction(transactionIndex, detailIndex, { amount: parseFloat(e.target.value) || 0 })}
                            />
                          </div>
                          <div className="col-span-2">
                            <CategoryPicker
                              value={detail.categoryId}
                              onValueChange={(value) => updateDetailTransaction(transactionIndex, detailIndex, { categoryId: value })}
                              placeholder="Category"
                              className="text-xs"
                            />
                          </div>
                          <div className="col-span-2">
                            <div className="space-y-2">
                              {/* Display current tags */}
                              {detail.selectedTags && detail.selectedTags.length > 0 && (
                                <div className="flex flex-wrap gap-1">
                                  {detail.selectedTags.map((tag) => (
                                    <TagChip
                                      key={tag.id}
                                      id={tag.id}
                                      name={tag.name}
                                      color={tag.color}
                                      size="sm"
                                      removable
                                      onRemove={(tagId) => {
                                        const updatedTags = detail.selectedTags?.filter(t => t.id !== tagId) || [];
                                        updateDetailTags(transactionIndex, detailIndex, updatedTags);
                                      }}
                                    />
                                  ))}
                                </div>
                              )}
                              
                              {/* Tag Selector */}
                              <TagSelector
                                selectedTags={detail.selectedTags || []}
                                onTagsChange={(newTags) => updateDetailTags(transactionIndex, detailIndex, newTags)}
                                placeholder="Add tags"
                                maxTags={5}
                              />
                              
                              {/* Display suggested tags if any */}
                              {((detail.suggestedTagIds && detail.suggestedTagIds.length > 0) || 
                                (detail.suggestedNewTags && detail.suggestedNewTags.length > 0)) && (
                                <div className="text-xs">
                                  <span className="text-muted-foreground">AI Suggestions: </span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {/* Existing suggested tags */}
                                    {detail.suggestedTagIds?.map(tagId => {
                                      const tag = tags?.find(t => t.id === tagId);
                                      if (!tag || detail.selectedTags?.some(st => st.id === tagId)) return null;
                                      return (
                                        <TagChip
                                          key={tag.id}
                                          id={tag.id}
                                          name={tag.name}
                                          color={tag.color}
                                          size="sm"
                                          variant="outline"
                                          onClick={() => {
                                            const updatedTags = [...(detail.selectedTags || []), tag];
                                            updateDetailTags(transactionIndex, detailIndex, updatedTags);
                                          }}
                                        />
                                      );
                                    })}
                                    {/* New suggested tags */}
                                    {detail.suggestedNewTags?.map(tagName => {
                                      if (detail.selectedTags?.some(st => st.name.toLowerCase() === tagName.toLowerCase())) return null;
                                      return (
                                        <TagChip
                                          key={tagName}
                                          id={`new-${tagName}`}
                                          name={tagName}
                                          size="sm"
                                          variant="secondary"
                                          onClick={() => {
                                            // Add to new tags list if not already there
                                            if (!newTags.some(nt => nt.name.toLowerCase() === tagName.toLowerCase())) {
                                              setNewTags(prev => [...prev, { name: tagName }]);
                                            }
                                            // Add to selected tags for this item
                                            const newTag: Tag = { id: `new-${tagName}`, name: tagName };
                                            const updatedTags = [...(detail.selectedTags || []), newTag];
                                            updateDetailTags(transactionIndex, detailIndex, updatedTags);
                                          }}
                                        />
                                      );
                                    })}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="col-span-1 flex justify-center">
                            <Switch
                              checked={detail.isIncome || false}
                              onCheckedChange={(checked) => updateDetailTransaction(transactionIndex, detailIndex, { isIncome: checked })}
                              size="sm"
                            />
                          </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Suggested categories */}
        {processedData.suggestedCategories.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Suggested Categories</CardTitle>
              <CardDescription>
                These categories will be created automatically when you save the transactions:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {processedData.suggestedCategories.map((category, index) => (
                  <Badge key={index} variant="outline">
                    {category}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Suggested tags */}
        {newTags.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Suggested Tags</CardTitle>
              <CardDescription>
                These tags will be created automatically when you save the transactions:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {newTags.map((tag, index) => (
                  <TagChip
                    key={index}
                    id={`new-${tag.name}`}
                    name={tag.name}
                    color={tag.color}
                    size="sm"
                    variant="secondary"
                    removable
                    onRemove={() => {
                      setNewTags(prev => prev.filter((_, i) => i !== index));
                      // Also remove from any selected tags
                      setEditingTransactions(prev => 
                        prev.map(transaction => ({
                          ...transaction,
                          detailsTransactions: transaction.detailsTransactions.map(detail => ({
                            ...detail,
                            selectedTags: detail.selectedTags?.filter(t => t.name !== tag.name) || []
                          }))
                        }))
                      );
                    }}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tag processing confidence */}
        {processedData.tagProcessingResult && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Tag Processing Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant={processedData.tagProcessingResult.confidence > 0.8 ? "default" : "secondary"}>
                    {Math.round(processedData.tagProcessingResult.confidence * 100)}% tag confidence
                  </Badge>
                </div>
                {processedData.tagProcessingResult.processingNotes && (
                  <p className="text-sm text-muted-foreground">
                    {processedData.tagProcessingResult.processingNotes}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Create custom category */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Create Custom Category</CardTitle>
            <CardDescription>
              Add a new category that will be created when saving transactions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Input
                placeholder="Enter new category name..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                    addNewCategory(e.currentTarget.value.trim());
                    e.currentTarget.value = '';
                  }
                }}
              />
              <Button
                type="button"
                variant="outline"
                onClick={(e) => {
                  const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                  if (input && input.value.trim()) {
                    addNewCategory(input.value.trim());
                    input.value = '';
                  }
                }}
              >
                Add Category
              </Button>
            </div>
            {newCategories.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2">
                {newCategories.map((category) => (
                  <Badge key={category.id} variant="secondary" className="flex items-center gap-1">
                    {category.name}
                    <button
                      onClick={() => setNewCategories(prev => prev.filter(c => c.id !== category.id))}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end gap-2">
          <Button onClick={resetForm} variant="outline">
            Cancel
          </Button>
          <Button onClick={createTransactions} disabled={isCreating}>
            {isCreating ? (
              <>Creating...</>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create {editingTransactions.length} Transaction{editingTransactions.length > 1 ? 's' : ''}
              </>
            )}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ReceiptUpload
        onReceiptsProcessed={handleReceiptsProcessed}
        onError={(error) => toast.error(error)}
        maxFiles={5}
      />
      
      {/* Help text */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-gray-600">
          <p>1. <strong>Upload receipts:</strong> Take photos or upload images/PDFs of your receipts</p>
          <p>2. <strong>AI processing:</strong> Our AI will extract transaction details and itemized lists</p>
          <p>3. <strong>Review & edit:</strong> Check and modify the extracted data as needed</p>
          <p>4. <strong>Assign accounts:</strong> Select which account each transaction should be assigned to</p>
          <p>5. <strong>Create transactions:</strong> Save the transactions to your financial records</p>
        </CardContent>
      </Card>
    </div>
  );
}